package quick.pager.shop.user.request;

import java.time.LocalDateTime;
import lombok.Data;

import java.io.Serializable;

/**
 * 请求基类
 *
 * <AUTHOR>
 */
@Data
public class Request implements Serializable {
    private static final long serialVersionUID = -8303934871640269088L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新操作人
     */
    private String updateUser;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 启用标志
     */
    private Boolean deleteStatus;

}
