server:
  port: 8095

spring:
  application:
    name: shop-auth
  autoconfigure:
    exclude: quick.pager.shop.configuration.ApplicationConfiguration
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: dev
  freemarker:
    template-loader-path: classpath:/templates/
management:
  endpoints:
    web:
      exposure:
        include: refresh,health,info

logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [${spring.application.name}] [traceId:%X{X-B3-TraceId}][spanId:%X{X-B3-SpanId}][parentSpanId:%X{X-B3-ParentSpanId}] --- [%t] - [%class:%method: %line] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [${spring.application.name}] [traceId:%X{X-B3-TraceId}][spanId:%X{X-B3-SpanId}][parentSpanId:%X{X-B3-ParentSpanId}] --- [%t] - [%class:%method: %line] - %msg%n"
  file:
    path: ./logs/${spring.application.name}
    max-size: 50MB
    name: ${spring.application.name}
