package quick.pager.shop.service;

/**
 * 地区服务
 *
 * <AUTHOR>
 * @version 3.0
 */
public interface AreaService {

//    @Autowired
//    private AreaMapper areaMapper;
//    @Autowired
//    private CityMapper cityMapper;
//    @Autowired
//    private ProvinceMapper provinceMapper;

//    @Override
//    public Response<AreaResponse> doService(BaseDTO dto) {
//
//        AreaResponse areaDTO = new AreaResponse();
//
////        List<Area> areas = areaMapper.selectAll();
////        List<City> cities = cityMapper.selectAll();
////        List<Province> provinces = provinceMapper.selectAll();
////
////        areaDTO.setAreas(areas);
////        areaDTO.setCities(cities);
////        areaDTO.setProvinces(provinces);
//
//        return new Response<>(areaDTO);
//    }
}
