<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="quick.pager.shop.mapper.SellerMapper">
    <resultMap id="BaseResultMap" type="quick.pager.shop.model.Seller">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="BIT" property="deleteStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, phone, password, create_time, update_time, delete_status
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_seller
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insertSelective" parameterType="quick.pager.shop.model.Seller">
        insert into t_seller
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleteStatus != null">
                delete_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteStatus != null">
                #{deleteStatus,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="quick.pager.shop.model.Seller">
        update t_seller
        <set>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteStatus != null">
                delete_status = #{deleteStatus,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
