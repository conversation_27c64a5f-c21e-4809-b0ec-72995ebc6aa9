sspring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.druid.filters=stat,wall,slf4j
spring.datasource.druid.initial-size=10
spring.datasource.druid.maxActive=20
spring.datasource.druid.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.druid.maxWait=60000
spring.datasource.druid.min-idle=10
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.stat-view-servlet.allow=************,localhost,127.0.0.1
spring.datasource.druid.stat-view-servlet.deny=************
spring.datasource.druid.stat-view-servlet.login-password=null
spring.datasource.druid.stat-view-servlet.login-username=null
spring.datasource.druid.stat-view-servlet.reset-enable=false
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.testWhileIdle = true
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.druid.validationQuery=SELECT 1
spring.datasource.druid.web-stat-filter.enabled=true
spring.datasource.druid.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*
spring.datasource.druid.web-stat-filter.url-pattern=/*
spring.datasource.password=xxx
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.url=**********************************************************************************************************************************
spring.datasource.username=xxx
spring.redis.host=xxx.xxx.xxx.xxx
spring.redis.port=6379
spring.redis.password=abc+123
