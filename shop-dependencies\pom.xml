<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <groupId>quick.pager</groupId>
    <artifactId>shop-dependencies</artifactId>

    <version>cloud-1.0</version>

    <name>spring::cloud::shop::dependencies</name>
    <description>Spring Cloud Shop Dependencies</description>

    <properties>
        <education.version>1.0</education.version>
        <junit.version>4.12</junit.version>
        <hutool.version>4.1.14</hutool.version>
        <easyexcel.version>2.2.6</easyexcel.version>
        <xerces.version>2.11.0</xerces.version>
        <fastjson.version>1.2.47</fastjson.version>
        <guava.version>20.0</guava.version>
        <qiniu.version>7.2.11</qiniu.version>
        <happy.version>0.1.4</happy.version>
        <httpclient.version>4.5.5</httpclient.version>
        <okhttp.version>3.14.7</okhttp.version>
        <mybatis.version>3.4.6</mybatis.version>
        <lombok.version>1.18.12</lombok.version>
        <spring-cloud.alibaba>2.2.1.RELEASE</spring-cloud.alibaba>
        <spring-boot.version>2.2.9.RELEASE</spring-boot.version>
        <spring-cloud-bus.version>2.2.2.RELEASE</spring-cloud-bus.version>
        <spring-cloud-openfeign.version>2.2.2.RELEASE</spring-cloud-openfeign.version>
        <spring-cloud-gateway.version>2.2.2.RELEASE</spring-cloud-gateway.version>
        <spring-cloud-commons.version>2.2.2.RELEASE</spring-cloud-commons.version>
        <spring-cloud-sleuth.version>2.2.2.RELEASE</spring-cloud-sleuth.version>
        <spring-cloud-netflix.version>2.2.2.RELEASE</spring-cloud-netflix.version>
        <spring-cloud-stream.version>Horsham.SR3</spring-cloud-stream.version>
        <mybatis-plus.version>3.3.0</mybatis-plus.version>
        <druid.version>1.1.23</druid.version>
        <cglib.version>3.2.10</cglib.version>
        <gson.version>2.8.5</gson.version>
        <commons-beanutils.version>1.9.3</commons-beanutils.version>
        <commons-collections4.version>4.2</commons-collections4.version>
        <aliyun.version>1.0.0</aliyun.version>
        <shardingsphere.version>4.1.0</shardingsphere.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-commons-dependencies</artifactId>
                <version>${spring-cloud-commons.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-sleuth-dependencies</artifactId>
                <version>${spring-cloud-sleuth.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-netflix-dependencies</artifactId>
                <version>${spring-cloud-netflix.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-openfeign-dependencies</artifactId>
                <version>${spring-cloud-openfeign.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-bus-dependencies</artifactId>
                <version>${spring-cloud-bus.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-gateway-dependencies</artifactId>
                <version>${spring-cloud-gateway.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-stream-dependencies</artifactId>
                <version>${spring-cloud-stream.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud.alibaba}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Aliyun Spring Boot dependencies -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>aliyun-spring-boot-dependencies</artifactId>
                <version>${aliyun.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
                <version>${shardingsphere.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${xerces.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>happy-dns-java</artifactId>
                <version>${happy.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>
