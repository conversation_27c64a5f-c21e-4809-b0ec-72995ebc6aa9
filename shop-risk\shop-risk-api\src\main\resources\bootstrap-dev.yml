spring:
  cloud:
    nacos:
      config:
        server-addr: *************:8848
        group: shop
        namespace: 0759bc76-60b5-4f32-acd5-e5095cf2b93d
        extension-configs:
          - data-id: shop-common.properties
            group: shop
            refresh: true
          - data-id: shop-redis.properties
            group: shop
            refresh: true
          - data-id: shop-rabbitmq.properties
            group: shop
            refresh: true
      discovery:
        server-addr: *************:8848
        group: shop
        namespace: 0759bc76-60b5-4f32-acd5-e5095cf2b93d
    sentinel:
      enabled: true
      transport:
        dashboard: 127.0.0.1:8080
      filter:
        enabled: true

seata:
  service:
    vgroup-mapping:
      shop-risk-seata-service-group: default
    grouplist:
      default: *************:8091
    disable-global-transaction: false


# 内部服务调用授权认证配置
security:
  oauth2:
    client:
      clientId: shop-client
      clientSecret: shop-client
      user-authorization-uri: http://localhost:8095/oauth/authorize
      access-token-uri: http://localhost:8095/oauth/token
      scope: app
    resource:
      id: shop-order
      user-info-uri: http://shop-auth/oauth/principal
      prefer-token-info: true
      loadBalanced: true
