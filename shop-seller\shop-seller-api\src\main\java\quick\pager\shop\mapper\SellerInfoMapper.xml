<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="quick.pager.shop.mapper.SellerInfoMapper">
    <resultMap id="BaseResultMap" type="quick.pager.shop.model.SellerInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="seller_name" jdbcType="VARCHAR" property="sellerName"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="seller_status" jdbcType="INTEGER" property="sellerStatus"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delete_status" jdbcType="BIT" property="deleteStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, phone, seller_name, address, seller_status, longitude, latitude, create_time,
    update_time, delete_status
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_seller_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insertSelective" parameterType="quick.pager.shop.model.SellerInfo">
        insert into t_seller_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="sellerName != null">
                seller_name,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="sellerStatus != null">
                seller_status,
            </if>
            <if test="longitude != null">
                longitude,
            </if>
            <if test="latitude != null">
                latitude,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleteStatus != null">
                delete_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="sellerName != null">
                #{sellerName,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="sellerStatus != null">
                #{sellerStatus,jdbcType=INTEGER},
            </if>
            <if test="longitude != null">
                #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteStatus != null">
                #{deleteStatus,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="quick.pager.shop.model.SellerInfo">
        update t_seller_info
        <set>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="sellerName != null">
                seller_name = #{sellerName,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="sellerStatus != null">
                seller_status = #{sellerStatus,jdbcType=INTEGER},
            </if>
            <if test="longitude != null">
                longitude = #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                latitude = #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteStatus != null">
                delete_status = #{deleteStatus,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
