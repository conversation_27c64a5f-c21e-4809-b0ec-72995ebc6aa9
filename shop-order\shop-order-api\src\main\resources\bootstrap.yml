server:
  port: 11400

spring:
  application:
    name: shop-order
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: dev
  shardingsphere:
    props:
      sql.show: true
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
seata:
  use-jdk-proxy: true
management:
  endpoints:
    web:
      exposure:
        include: refresh,health,info,hystrix.stream
mybatis-plus:
  type-aliases-package: quick.pager.shop.model
  global-config:
    db-config:
      id-type: auto
  type-handlers-package: quick.pager.shop.mybatis
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [${spring.application.name}] [traceId:%X{X-B3-TraceId}][spanId:%X{X-B3-SpanId}][parentSpanId:%X{X-B3-ParentSpanId}] --- [%t] - [%class:%method: %line] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [${spring.application.name}] [traceId:%X{X-B3-TraceId}][spanId:%X{X-B3-SpanId}][parentSpanId:%X{X-B3-ParentSpanId}] --- [%t] - [%class:%method: %line] - %msg%n"
  level:
    org.springframework: error
    com.alibaba: error
    org.apache.ibatis: error
    io.seata: error
  file:
    path: ./logs/${spring.application.name}
    max-size: 50MB
    name: ${spring.application.name}
