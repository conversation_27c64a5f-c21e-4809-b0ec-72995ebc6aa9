spring:
  cloud:
    nacos:
      config:
        server-addr: 106.54.251.32:8848
        group: shop
        namespace: 0759bc76-60b5-4f32-acd5-e5095cf2b93d
        extension-configs:
          - data-id: shop-common.properties
            group: shop
            refresh: true
          - data-id: shop-redis.properties
            group: shop
            refresh: true
      discovery:
        server-addr: 106.54.251.32:8848
        group: shop
        namespace: 0759bc76-60b5-4f32-acd5-e5095cf2b93d
    sentinel:
      enabled: true
      transport:
        dashboard: 127.0.0.1:8080
      filter:
        enabled: true
