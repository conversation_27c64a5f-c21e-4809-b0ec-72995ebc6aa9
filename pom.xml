<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>quick.pager</groupId>
    <artifactId>spring-cloud-shop</artifactId>
    <packaging>pom</packaging>
    <version>cloud-1.0</version>

    <developers>
        <developer>
            <id>SiGuiyang</id>
            <name>SiG<PERSON>yang</name>
        </developer>
    </developers>

    <organization>
        <name>SiGuiyang</name>
        <url>https://github.com/SiGuiyang</url>
    </organization>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/SiGuiyang/spring-cloud-shop/issues</url>
    </issueManagement>

    <scm>
        <url>**************:seata/seata.git</url>
        <connection>scm:**************:SiGuiyang/spring-cloud-shop.git</connection>
        <developerConnection>scm:**************:SiGuiyang/spring-cloud-shop.git</developerConnection>
    </scm>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>quick.pager</groupId>
                <artifactId>shop-dependencies</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>5.5.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <scope>test</scope>
                <version>3.5.7</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <scope>test</scope>
                <version>3.5.7</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>3.5.7</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.5.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
            <version>3.5.7</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
            <version>3.5.7</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>3.5.7</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>aliyun</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>  <!-- 请使用最新稳定版本号 -->
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal> <!-- 绑定JaCoCo到Maven测试阶段 -->
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id> <!-- 为报告提供一个ID -->
                        <phase>test</phase> <!-- 绑定报告生成到测试阶段 -->
                        <goals>
                            <goal>report</goal> <!-- 生成报告 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>shop-activity</module>
        <module>shop-auth</module>
        <module>shop-cart</module>
        <module>shop-common</module>
        <module>shop-dependencies</module>
        <module>shop-elasticsearch</module>
        <module>shop-gateway</module>
        <module>shop-goods</module>
        <module>shop-job</module>
        <module>shop-manage</module>
        <module>shop-order</module>
        <module>shop-oss</module>
        <module>shop-platform</module>
        <module>shop-risk</module>
        <module>shop-seller</module>
        <module>shop-settlement</module>
        <module>shop-user</module>
        <module>sharding-transaction-base-seata-at</module>
    </modules>
</project>
