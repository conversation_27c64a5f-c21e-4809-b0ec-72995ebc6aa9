<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>shop-settlement</artifactId>
        <groupId>quick.pager</groupId>
        <version>cloud-1.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shop-settlement-api</artifactId>

    <dependencies>
        <!-- start spring cloud alibaba -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        <!-- end spring cloud alibaba -->

        <!-- start spring cloud -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>org.springframework.cloud</groupId>-->
            <!--<artifactId>spring-cloud-starter-sleuth</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
        </dependency>
        <!-- end spring cloud -->

        <!-- start spring boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- end spring boot -->

        <!-- start 客户端授权-->
        <dependency>
            <groupId>quick.pager</groupId>
            <artifactId>shop-auth-resource</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>quick.pager</groupId>
                    <artifactId>shop-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- end 客户端授权-->

        <dependency>
            <groupId>quick.pager</groupId>
            <artifactId>shop-settlement-model</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>quick.pager</groupId>
                    <artifactId>shop-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>quick.pager</groupId>
            <artifactId>shop-activity-client</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>quick.pager</groupId>
                    <artifactId>shop-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>quick.pager</groupId>
            <artifactId>shop-goods-client</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>quick.pager</groupId>
                    <artifactId>shop-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>quick.pager</groupId>
            <artifactId>shop-cart-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>quick.pager</groupId>
            <artifactId>shop-order-client</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>quick.pager</groupId>
                    <artifactId>shop-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>quick.pager</groupId>
            <artifactId>shop-platform-client</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>quick.pager</groupId>
                    <artifactId>shop-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>quick.pager</groupId>
            <artifactId>shop-user-client</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>quick.pager</groupId>
                    <artifactId>shop-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>quick.pager</groupId>
            <artifactId>shop-common</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.9.RELEASE</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
