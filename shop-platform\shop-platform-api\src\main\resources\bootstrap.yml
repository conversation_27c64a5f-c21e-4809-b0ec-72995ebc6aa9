server:
  port: 11500

spring:
  application:
    name: shop-platform
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: dev

feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false

shop:
  oauth:
    permissions: /platform/**

management:
  endpoints:
    web:
      exposure:
        include: refresh,health,info

mybatis-plus:
  type-aliases-package: quick.pager.shop.model
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto

logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [${spring.application.name}] [traceId:%X{X-B3-TraceId}][spanId:%X{X-B3-SpanId}][parentSpanId:%X{X-B3-ParentSpanId}] --- [%t] - [%class:%method: %line] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [${spring.application.name}] [traceId:%X{X-B3-TraceId}][spanId:%X{X-B3-SpanId}][parentSpanId:%X{X-B3-ParentSpanId}] --- [%t] - [%class:%method: %line] - %msg%n"
  level:
    org.springframework: error
    com.alibaba: error
    org.apache.ibatis: error
    io.seata: error
  file:
    path: ./logs/${spring.application.name}
    max-size: 50MB
    name: ${spring.application.name}
