<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>spring-cloud-shop</artifactId>
        <groupId>quick.pager</groupId>
        <version>cloud-1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>quick.pager</groupId>
    <artifactId>sharding-transaction-base-seata-at</artifactId>

    <dependencies>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-transaction-core</artifactId>
            <version>4.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-rm-datasource</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-tm</artifactId>
            <version>1.1.0</version>
        </dependency>
    </dependencies>

</project>
