server:
  port: 10000

spring:
  profiles:
    active: dev
  application:
    name: shop-gateway
  cloud:
    gateway:
      discovery:
        locator:
          lower-case-service-id: true
          enabled: true
      routes:
        - id: shop-auth
          uri: lb://shop-auth
          predicates:
            - Path=/oauth/**
        - id: shop-activity
          uri: lb://shop-activity
          predicates:
            - Path=/activity/**
        - id: shop-manage
          uri: lb://shop-manage
          predicates:
            - Path=/admin/**
        - id: shop-platform
          uri: lb://shop-platform
          predicates:
            - Path=/platform/**
        - id: shop-risk
          uri: lb://shop-risk
          predicates:
            - Path=/risk/**
        - id: shop-seller
          uri: lb://shop-seller
          predicates:
            - Path=/seller/**
        - id: shop-user
          uri: lb://shop-user
          predicates:
            - Path=/user/app/**
            - Path=/user/**
        - id: shop-order
          uri: lb://shop-order
          predicates:
            - Path=/order/**
        - id: shop-goods
          uri: lb://shop-goods
          predicates:
            - Path=/goods/**
        - id: shop-oss
          uri: lb://shop-oss
          predicates:
            - Path=/oss/**
        - id: shop-cart
          uri: lb://shop-cart
          predicates:
            - Path=/cart/**
        - id: shop-settlement
          uri: lb://shop-settlement
          predicates:
            - Path=/settlement/**
        - id: shop-job
          uri: lb://shop-job
          predicates:
            - Path=/job/**
management:
  endpoints:
    web:
      exposure:
        include: refresh,health,info
logging:
  level:
    org.springframework.security: debug
    com.alibaba.nacos: error
