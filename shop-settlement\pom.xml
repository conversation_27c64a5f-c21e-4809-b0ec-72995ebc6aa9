<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <modules>
        <module>shop-settlement-api</module>
        <module>shop-settlement-client</module>
        <module>shop-settlement-model</module>
    </modules>

    <parent>
        <artifactId>spring-cloud-shop</artifactId>
        <groupId>quick.pager</groupId>
        <version>cloud-1.0</version>
    </parent>
    <artifactId>shop-settlement</artifactId>
    <packaging>pom</packaging>

    <description>清结算服务</description>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
